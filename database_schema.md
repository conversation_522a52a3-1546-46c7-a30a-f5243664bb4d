# SmartHire AI Resume Builder - Database Schema

## MongoDB Collections Design

### 1. Users Collection
```javascript
{
  "_id": ObjectId,
  "email": "<EMAIL>",
  "password_hash": "bcrypt_hashed_password",
  "name": "<PERSON>",
  "profile_photo": "uploads/profile_photos/user_id.jpg", // Optional
  "created_at": ISODate,
  "updated_at": ISODate,
  "is_active": true,
  "last_login": ISODate
}
```

### 2. Resumes Collection
```javascript
{
  "_id": ObjectId,
  "user_id": ObjectId, // Reference to Users collection
  "title": "Python Developer Resume",
  "theme": "professional", // professional, modern, creative, minimalist, executive, tech
  "theme_settings": {
    "primary_color": "#007bff",
    "secondary_color": "#6c757d",
    "background_color": "#ffffff",
    "pin_icon_color": "#28a745",
    "custom_background": "uploads/backgrounds/resume_id.jpg" // Optional
  },
  "personal_info": {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "address": "123 Main St, City, State 12345",
    "linkedin": "https://linkedin.com/in/johndoe", // Optional
    "github": "https://github.com/johndoe", // Optional
    "website": "https://johndoe.com" // Optional
  },
  "sections": {
    "objective": {
      "content": "Experienced Python developer...",
      "ai_generated": true,
      "last_ai_update": ISODate
    },
    "experience": [
      {
        "company": "Tech Corp",
        "position": "Senior Developer",
        "start_date": "2020-01",
        "end_date": "2023-12", // null for current
        "description": "Led development of...",
        "ai_generated": false
      }
    ],
    "skills": {
      "technical": ["Python", "JavaScript", "React"],
      "soft": ["Leadership", "Communication"],
      "content": "Python, JavaScript, React...", // For display
      "ai_generated": false
    },
    "education": [
      {
        "institution": "University Name",
        "degree": "Bachelor of Computer Science",
        "start_date": "2016-09",
        "end_date": "2020-05",
        "gpa": "3.8" // Optional
      }
    ],
    "projects": [
      {
        "name": "E-commerce Platform",
        "description": "Built a full-stack...",
        "technologies": ["React", "Node.js"],
        "url": "https://github.com/johndoe/project", // Optional
        "ai_generated": false
      }
    ],
    "achievements": {
      "content": "Won hackathon, Published paper...",
      "ai_generated": true,
      "last_ai_update": ISODate
    },
    "languages": {
      "content": "English (Native), Spanish (Fluent)",
      "ai_generated": false
    },
    "activities": {
      "content": "Volunteer at local shelter...",
      "ai_generated": false
    },
    "references": {
      "content": "Available upon request",
      "ai_generated": false
    },
    "about_me": {
      "content": "Passionate developer with...",
      "ai_generated": true,
      "last_ai_update": ISODate
    }
  },
  "ats_score": {
    "score": 85,
    "last_calculated": ISODate,
    "suggestions": [
      "Add more keywords related to your industry",
      "Include quantifiable achievements"
    ]
  },
  "created_at": ISODate,
  "updated_at": ISODate,
  "is_active": true
}
```

### 3. Themes Collection
```javascript
{
  "_id": ObjectId,
  "name": "professional",
  "display_name": "Professional",
  "description": "Clean and professional design perfect for corporate roles",
  "preview_image": "static/theme_previews/professional.jpg",
  "css_template": "themes/professional.css",
  "default_colors": {
    "primary": "#007bff",
    "secondary": "#6c757d",
    "background": "#ffffff",
    "text": "#333333"
  },
  "is_active": true,
  "created_at": ISODate
}
```

### 4. AI_Generations Collection (For tracking AI usage)
```javascript
{
  "_id": ObjectId,
  "user_id": ObjectId,
  "resume_id": ObjectId,
  "section": "objective", // objective, experience, achievements, about_me
  "prompt": "Generate professional objective for Python developer",
  "generated_content": "Experienced Python developer...",
  "model_used": "gpt-3.5-turbo",
  "tokens_used": 150,
  "created_at": ISODate
}
```

### 5. User_Sessions Collection (For enhanced session management)
```javascript
{
  "_id": ObjectId,
  "user_id": ObjectId,
  "session_token": "jwt_token_here",
  "expires_at": ISODate,
  "created_at": ISODate,
  "last_activity": ISODate,
  "ip_address": "***********",
  "user_agent": "Mozilla/5.0..."
}
```

## Indexes for Performance

```javascript
// Users Collection
db.users.createIndex({ "email": 1 }, { unique: true })
db.users.createIndex({ "created_at": -1 })

// Resumes Collection
db.resumes.createIndex({ "user_id": 1, "created_at": -1 })
db.resumes.createIndex({ "user_id": 1, "is_active": 1 })
db.resumes.createIndex({ "theme": 1 })

// AI_Generations Collection
db.ai_generations.createIndex({ "user_id": 1, "created_at": -1 })
db.ai_generations.createIndex({ "resume_id": 1 })

// User_Sessions Collection
db.user_sessions.createIndex({ "session_token": 1 }, { unique: true })
db.user_sessions.createIndex({ "expires_at": 1 }, { expireAfterSeconds: 0 })
db.user_sessions.createIndex({ "user_id": 1 })
```

## Migration from Current Schema

Your current `resumes` collection data can be migrated to the new schema:
- Map existing fields to `sections` object
- Add default theme settings
- Create user accounts for existing resumes
- Set default ATS scores

## Key Features Supported

1. **Multi-user system** with proper authentication
2. **Multiple resumes per user** with different themes
3. **AI content tracking** for usage analytics
4. **Theme customization** with color schemes
5. **ATS scoring** with improvement suggestions
6. **Session management** for security
7. **Audit trail** for AI generations
8. **Scalable design** for future features
