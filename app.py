# SmartHire - AI Resume Builder

from flask import Flask, render_template, request, session, redirect, url_for, flash
from flask_pymongo import PyMongo

app = Flask(__name__)

app.config["MONGO_URI"] = "mongodb://localhost:27017/ai_resume_db"
mongo = PyMongo(app)

@app.route('/submit', methods=['POST'])
def submit():
    # Retrieve step 1 data from session
    name = session.get('name')
    email = session.get('email')
    address = session.get('address')
    phone = session.get('phone')
    if not all([name, email, address, phone]):
        return redirect(url_for('form1'))
    # Retrieve step 2 data from form
    languages = request.form.get('languages')
    activities = request.form.get('activities')
    about_me = request.form.get('about_me')
    skills = request.form.get('skills')
    experience = request.form.get('experience')
    objective = request.form.get('objective')
    reference = request.form.get('reference')
    projects = request.form.get('projects')
    achievements = request.form.get('achievements')

    data = {
        "name": name,
        "email": email,
        "address": address,
        "phone": phone,
        "languages": languages,
        "activities": activities,
        "about_me": about_me,
        "skills": skills,
        "experience": experience,
        "objective": objective,
        "reference": reference,
        "projects": projects,
        "achievements": achievements
    }
        
    mongo.db.resumes.insert_one(data)
    return render_template('result.html', data=data)

app.secret_key = 'your_secret_key'  # Needed for session
USERNAME = 'admin'
PASSWORD = 'admin123'

@app.route('/')
def home():
    return render_template('home.html')

@app.route('/login')
def login():
    return render_template('login.html')

@app.route('/login', methods=['POST'])
def login_user():
    password = request.form.get('password')

    if password == PASSWORD:
        return redirect(url_for('dashboard'))  # Redirect to dashboard after successful login
    else:
        return '<h3>Invalid password. <a href="/login">Try again</a></h3>'

@app.route('/get-started')
def get_started():
    return redirect(url_for('login'))

@app.route('/signup', methods=['POST'])
def signup_user():
    fullname = request.form.get('fullname')
    email = request.form.get('email')
    password = request.form.get('password')

    # Handle photo upload
    photo = request.files.get('photo')
    photo_filename = None

    if photo and photo.filename:
        # Create uploads directory if it doesn't exist
        import os
        upload_dir = 'static/uploads'
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir)

        # Save the photo with a unique filename
        import uuid
        file_extension = photo.filename.rsplit('.', 1)[1].lower() if '.' in photo.filename else 'jpg'
        photo_filename = f"{uuid.uuid4()}.{file_extension}"
        photo.save(os.path.join(upload_dir, photo_filename))

        print(f"Photo uploaded: {photo_filename}")

    print(f"Signup: {fullname}, {email}, Photo: {photo_filename}")

    # For demo purposes, just redirect to dashboard
    # In a real app, you'd save to database and validate
    return redirect(url_for('dashboard'))
    
    
@app.route('/dashboard')
def dashboard():
    # This will be the resume dashboard after login
    return render_template('dashboard.html')

@app.route('/form1')
def form1():
    return render_template('form1.html')

@app.route('/form_step2', methods=['POST'])
def form_step2():
    # Save step 1 data in session
    session['name'] = request.form.get('name')
    session['email'] = request.form.get('email')
    session['address'] = request.form.get('address')
    session['phone'] = request.form.get('phone')
    print("Session after step 1:", dict(session))
    return render_template('form2.html')

@app.route('/preview')
def preview():
    # Pass data from session or database as needed
    data = session.copy()
    return render_template('preview.html', data=data)

if __name__ == '__main__':
    app.run(debug=True)