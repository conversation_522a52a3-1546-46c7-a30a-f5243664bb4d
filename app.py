# SmartHire - AI Resume Builder
# Enhanced version with JWT authentication, AI integration, and professional features

from flask import Flask, render_template, request, session, redirect, url_for, flash, jsonify
from flask_pymongo import PyMongo
from flask_jwt_extended import J<PERSON><PERSON>anager, jwt_required, create_access_token, get_jwt_identity
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
from datetime import datetime, timedelta
import uuid
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)

# Configuration
app.config["MONGO_URI"] = os.getenv("MONGO_URI", "mongodb://localhost:27017/smarthire_db?connectTimeoutMS=2000&serverSelectionTimeoutMS=2000")
app.config["JWT_SECRET_KEY"] = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production")
app.config["JWT_ACCESS_TOKEN_EXPIRES"] = timedelta(hours=24)
app.config["UPLOAD_FOLDER"] = "static/uploads"
app.config["MAX_CONTENT_LENGTH"] = 16 * 1024 * 1024  # 16MB max file size

# Initialize extensions
try:
    mongo = PyMongo(app)
    # Test the connection
    mongo.db.list_collection_names()
    print("MongoDB connection successful")
except Exception as e:
    print(f"MongoDB connection failed: {e}")
    print("App will run in demo mode without database functionality")
    mongo = None

jwt = JWTManager(app)
print("JWT Manager initialized")

# OpenAI configuration
try:
    from openai import OpenAI
    openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY")) if os.getenv("OPENAI_API_KEY") else None
    print("OpenAI client initialized" if openai_client else "OpenAI client not configured")
except Exception as e:
    print(f"OpenAI initialization failed: {e}")
    openai_client = None

# Ensure upload directories exist
os.makedirs(app.config["UPLOAD_FOLDER"], exist_ok=True)
os.makedirs(os.path.join(app.config["UPLOAD_FOLDER"], "profile_photos"), exist_ok=True)
os.makedirs(os.path.join(app.config["UPLOAD_FOLDER"], "backgrounds"), exist_ok=True)

# Import models and utilities (temporarily commented out for debugging)
# from models import UserModel, ResumeModel, ThemeModel, AIGenerationModel, DEFAULT_THEMES, allowed_file, generate_unique_filename
# from utils import AIContentGenerator, ATSScoreAnalyzer, format_resume_for_pdf

# Initialize AI content generator
# ai_generator = AIContentGenerator(openai_client) if openai_client else None
ai_generator = None

# Initialize default themes in database
def init_default_themes():
    """Initialize default themes if they don't exist"""
    if not mongo:
        print("MongoDB not available - skipping theme initialization")
        return

    try:
        print("Checking existing themes...")
        existing_themes = mongo.db.themes.count_documents({})
        print(f"Found {existing_themes} existing themes")
        if existing_themes == 0:
            print("Inserting default themes...")
            mongo.db.themes.insert_many(DEFAULT_THEMES)
            print("Default themes initialized")
        else:
            print("Default themes already exist")
    except Exception as e:
        print(f"Error initializing themes: {e}")

# Note: Themes will be initialized when the app starts running

# JWT Error handlers
@jwt.expired_token_loader
def expired_token_callback(jwt_header, jwt_payload):
    return jsonify({'message': 'Token has expired'}), 401

@jwt.invalid_token_loader
def invalid_token_callback(error):
    return jsonify({'message': 'Invalid token'}), 401

@jwt.unauthorized_loader
def missing_token_callback(error):
    return jsonify({'message': 'Token is required'}), 401

@app.route('/submit', methods=['POST'])
def submit():
    # Retrieve step 1 data from session
    name = session.get('name')
    email = session.get('email')
    address = session.get('address')
    phone = session.get('phone')
    if not all([name, email, address, phone]):
        return redirect(url_for('form1'))
    # Retrieve step 2 data from form
    languages = request.form.get('languages')
    activities = request.form.get('activities')
    about_me = request.form.get('about_me')
    skills = request.form.get('skills')
    experience = request.form.get('experience')
    objective = request.form.get('objective')
    reference = request.form.get('reference')
    projects = request.form.get('projects')
    achievements = request.form.get('achievements')

    data = {
        "name": name,
        "email": email,
        "address": address,
        "phone": phone,
        "languages": languages,
        "activities": activities,
        "about_me": about_me,
        "skills": skills,
        "experience": experience,
        "objective": objective,
        "reference": reference,
        "projects": projects,
        "achievements": achievements
    }
        
    mongo.db.resumes.insert_one(data)
    return render_template('result.html', data=data)

app.secret_key = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")

# ============================================================================
# AUTHENTICATION ROUTES
# ============================================================================

@app.route('/')
def home():
    """Home page"""
    return render_template('home.html')

@app.route('/login')
def login_page():
    """Login page"""
    return render_template('auth.html')

@app.route('/signup')
def signup_page():
    """Signup page"""
    return render_template('auth.html')

@app.route('/api/auth/login', methods=['POST'])
def login_user():
    """User login API"""
    try:
        data = request.get_json()
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')

        if not email or not password:
            return jsonify({'error': 'Email and password are required'}), 400

        # Find user
        user = UserModel.find_by_email(mongo, email)
        if not user or not check_password_hash(user['password_hash'], password):
            return jsonify({'error': 'Invalid email or password'}), 401

        # Update last login
        UserModel.update_last_login(mongo, str(user['_id']))

        # Create access token
        access_token = create_access_token(identity=str(user['_id']))

        return jsonify({
            'access_token': access_token,
            'user': {
                'id': str(user['_id']),
                'name': user['name'],
                'email': user['email'],
                'profile_photo': user.get('profile_photo')
            }
        }), 200

    except Exception as e:
        return jsonify({'error': 'Login failed'}), 500

@app.route('/api/auth/signup', methods=['POST'])
def signup_user():
    """User signup API"""
    try:
        # Handle both form data and JSON
        if request.content_type.startswith('multipart/form-data'):
            name = request.form.get('name', '').strip()
            email = request.form.get('email', '').strip().lower()
            password = request.form.get('password', '')
            photo = request.files.get('photo')
        else:
            data = request.get_json()
            name = data.get('name', '').strip()
            email = data.get('email', '').strip().lower()
            password = data.get('password', '')
            photo = None

        # Validation
        if not name or not email or not password:
            return jsonify({'error': 'Name, email, and password are required'}), 400

        if len(password) < 6:
            return jsonify({'error': 'Password must be at least 6 characters'}), 400

        # Check if user already exists
        if UserModel.find_by_email(mongo, email):
            return jsonify({'error': 'User with this email already exists'}), 409

        # Handle photo upload
        photo_filename = None
        if photo and photo.filename and allowed_file(photo.filename):
            photo_filename = generate_unique_filename(photo.filename)
            photo_path = os.path.join(app.config["UPLOAD_FOLDER"], "profile_photos", photo_filename)
            photo.save(photo_path)
            photo_filename = f"uploads/profile_photos/{photo_filename}"

        # Create user
        password_hash = generate_password_hash(password)
        user_id = UserModel.create_user(mongo, email, password_hash, name, photo_filename)

        # Create access token
        access_token = create_access_token(identity=str(user_id))

        return jsonify({
            'access_token': access_token,
            'user': {
                'id': str(user_id),
                'name': name,
                'email': email,
                'profile_photo': photo_filename
            }
        }), 201

    except Exception as e:
        return jsonify({'error': 'Signup failed'}), 500

@app.route('/get-started')
def get_started():
    """Redirect to login"""
    return redirect(url_for('login_page'))

# ============================================================================
# DASHBOARD ROUTES
# ============================================================================

@app.route('/dashboard')
@jwt_required(optional=True)
def dashboard():
    """Resume dashboard page"""
    return render_template('dashboard_new.html')

@app.route('/api/resumes', methods=['GET'])
@jwt_required()
def get_user_resumes():
    """Get all resumes for current user"""
    try:
        user_id = get_jwt_identity()
        resumes = ResumeModel.find_by_user(mongo, user_id)

        # Convert ObjectId to string for JSON serialization
        for resume in resumes:
            resume['_id'] = str(resume['_id'])
            resume['user_id'] = str(resume['user_id'])

        return jsonify({'resumes': resumes}), 200
    except Exception as e:
        return jsonify({'error': 'Failed to fetch resumes'}), 500

@app.route('/api/resumes', methods=['POST'])
@jwt_required()
def create_resume():
    """Create new resume"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        title = data.get('title', '').strip()
        theme = data.get('theme', 'professional')

        if not title:
            return jsonify({'error': 'Resume title is required'}), 400

        resume_id = ResumeModel.create_resume(mongo, user_id, title, theme)

        return jsonify({
            'resume_id': str(resume_id),
            'message': 'Resume created successfully'
        }), 201
    except Exception as e:
        return jsonify({'error': 'Failed to create resume'}), 500

@app.route('/api/resumes/<resume_id>', methods=['GET'])
@jwt_required()
def get_resume(resume_id):
    """Get specific resume"""
    try:
        user_id = get_jwt_identity()
        resume = ResumeModel.find_by_id(mongo, resume_id, user_id)

        if not resume:
            return jsonify({'error': 'Resume not found'}), 404

        # Convert ObjectId to string
        resume['_id'] = str(resume['_id'])
        resume['user_id'] = str(resume['user_id'])

        return jsonify({'resume': resume}), 200
    except Exception as e:
        return jsonify({'error': 'Failed to fetch resume'}), 500

@app.route('/api/resumes/<resume_id>', methods=['PUT'])
@jwt_required()
def update_resume(resume_id):
    """Update resume"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        # Remove fields that shouldn't be updated directly
        update_data = {k: v for k, v in data.items() if k not in ['_id', 'user_id', 'created_at']}

        success = ResumeModel.update_resume(mongo, resume_id, user_id, update_data)

        if not success:
            return jsonify({'error': 'Resume not found or update failed'}), 404

        return jsonify({'message': 'Resume updated successfully'}), 200
    except Exception as e:
        return jsonify({'error': 'Failed to update resume'}), 500

@app.route('/api/resumes/<resume_id>', methods=['DELETE'])
@jwt_required()
def delete_resume(resume_id):
    """Delete resume"""
    try:
        user_id = get_jwt_identity()
        success = ResumeModel.delete_resume(mongo, resume_id, user_id)

        if not success:
            return jsonify({'error': 'Resume not found'}), 404

        return jsonify({'message': 'Resume deleted successfully'}), 200
    except Exception as e:
        return jsonify({'error': 'Failed to delete resume'}), 500

# ============================================================================
# AI CONTENT GENERATION ROUTES
# ============================================================================

@app.route('/api/ai/generate', methods=['POST'])
@jwt_required()
def generate_ai_content():
    """Generate AI content for resume sections"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        resume_id = data.get('resume_id')
        section = data.get('section')
        user_data = data.get('user_data', {})
        job_title = data.get('job_title', '')
        industry = data.get('industry', '')

        if not resume_id or not section:
            return jsonify({'error': 'Resume ID and section are required'}), 400

        # Verify resume belongs to user
        resume = ResumeModel.find_by_id(mongo, resume_id, user_id)
        if not resume:
            return jsonify({'error': 'Resume not found'}), 404

        # Generate content based on section
        generated_content = ""
        if section == 'objective':
            generated_content = AIContentGenerator.generate_objective(user_data, job_title, industry)
        elif section == 'experience':
            generated_content = AIContentGenerator.generate_experience_description(
                job_title, user_data.get('company', ''), user_data.get('responsibilities', '')
            )
        elif section == 'achievements':
            generated_content = AIContentGenerator.generate_achievements(user_data)
        elif section == 'about_me':
            generated_content = AIContentGenerator.generate_about_me(user_data)
        else:
            return jsonify({'error': 'Invalid section for AI generation'}), 400

        # Log the generation
        AIGenerationModel.log_generation(
            mongo, user_id, resume_id, section,
            f"Generate {section} for {job_title}", generated_content
        )

        # Update resume with AI-generated content
        update_data = {
            f'sections.{section}.content': generated_content,
            f'sections.{section}.ai_generated': True,
            f'sections.{section}.last_ai_update': datetime.utcnow()
        }
        ResumeModel.update_resume(mongo, resume_id, user_id, update_data)

        return jsonify({
            'content': generated_content,
            'section': section,
            'message': 'Content generated successfully'
        }), 200

    except Exception as e:
        return jsonify({'error': 'Failed to generate AI content'}), 500

# ============================================================================
# ATS SCORE ROUTES
# ============================================================================

@app.route('/api/ats/score/<resume_id>', methods=['GET'])
@jwt_required()
def calculate_ats_score(resume_id):
    """Calculate ATS score for resume"""
    try:
        user_id = get_jwt_identity()
        resume = ResumeModel.find_by_id(mongo, resume_id, user_id)

        if not resume:
            return jsonify({'error': 'Resume not found'}), 404

        # Calculate ATS score
        ats_result = ATSScoreAnalyzer.calculate_ats_score(resume)

        # Update resume with new score
        update_data = {'ats_score': ats_result}
        ResumeModel.update_resume(mongo, resume_id, user_id, update_data)

        return jsonify(ats_result), 200

    except Exception as e:
        return jsonify({'error': 'Failed to calculate ATS score'}), 500

# ============================================================================
# THEME ROUTES
# ============================================================================

@app.route('/api/themes', methods=['GET'])
def get_themes():
    """Get all available themes"""
    try:
        themes = ThemeModel.get_all_themes(mongo)

        # Convert ObjectId to string
        for theme in themes:
            theme['_id'] = str(theme['_id'])

        return jsonify({'themes': themes}), 200
    except Exception as e:
        return jsonify({'error': 'Failed to fetch themes'}), 500

# ============================================================================
# RESUME BUILDER ROUTES
# ============================================================================

@app.route('/builder/<resume_id>')
@jwt_required(optional=True)
def resume_builder(resume_id):
    """Resume builder page"""
    return render_template('builder.html', resume_id=resume_id)

@app.route('/form1')
def form1():
    return render_template('form1.html')

@app.route('/form_step2', methods=['POST'])
def form_step2():
    # Save step 1 data in session
    session['name'] = request.form.get('name')
    session['email'] = request.form.get('email')
    session['address'] = request.form.get('address')
    session['phone'] = request.form.get('phone')
    print("Session after step 1:", dict(session))
    return render_template('form2.html')

@app.route('/preview')
def preview():
    # Pass data from session or database as needed
    data = session.copy()
    return render_template('preview.html', data=data)

if __name__ == '__main__':
    print("Starting SmartHire application...")
    if mongo:
        print("MongoDB connected - themes will be initialized on first access")
        # init_default_themes()  # Commented out to prevent startup hang
    else:
        print("Warning: MongoDB not connected. Some features may not work.")
    print("Starting Flask server on http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
    app.run(debug=True, host='0.0.0.0', port=5000)