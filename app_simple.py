#!/usr/bin/env python3
"""
Simple Resume Builder App - Working Version
"""

from flask import Flask, render_template, request, redirect, url_for, flash
import os

print("Starting Resume Builder...")

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'simple-secret-key'

print("Flask app created successfully")

@app.route('/')
def home():
    """Home page"""
    return render_template('home.html')

@app.route('/login')
def login():
    """Login page"""
    return render_template('login.html')

@app.route('/dashboard')
def dashboard():
    """Dashboard page"""
    return render_template('dashboard.html')

@app.route('/form1')
def form1():
    """Form 1 page"""
    return render_template('form1.html')

@app.route('/form2')
def form2():
    """Form 2 page"""
    return render_template('form2.html')

@app.route('/preview')
def preview():
    """Preview page"""
    return render_template('preview.html')

if __name__ == '__main__':
    print("Starting simple Resume Builder server...")
    print("Server will be available at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
