# SmartHire - Database Models and Utilities

from datetime import datetime
from bson import ObjectId
import uuid

class UserModel:
    """User model for SmartHire application"""
    
    @staticmethod
    def create_user(mongo, email, password_hash, name, profile_photo=None):
        """Create a new user"""
        user_data = {
            "email": email.lower(),
            "password_hash": password_hash,
            "name": name,
            "profile_photo": profile_photo,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "is_active": True,
            "last_login": None
        }
        result = mongo.db.users.insert_one(user_data)
        return result.inserted_id
    
    @staticmethod
    def find_by_email(mongo, email):
        """Find user by email"""
        return mongo.db.users.find_one({"email": email.lower()})
    
    @staticmethod
    def find_by_id(mongo, user_id):
        """Find user by ID"""
        return mongo.db.users.find_one({"_id": ObjectId(user_id)})
    
    @staticmethod
    def update_last_login(mongo, user_id):
        """Update user's last login time"""
        mongo.db.users.update_one(
            {"_id": ObjectId(user_id)},
            {"$set": {"last_login": datetime.utcnow()}}
        )

class ResumeModel:
    """Resume model for SmartHire application"""
    
    @staticmethod
    def create_resume(mongo, user_id, title, theme="professional"):
        """Create a new resume"""
        resume_data = {
            "user_id": ObjectId(user_id),
            "title": title,
            "theme": theme,
            "theme_settings": {
                "primary_color": "#007bff",
                "secondary_color": "#6c757d",
                "background_color": "#ffffff",
                "pin_icon_color": "#28a745",
                "custom_background": None
            },
            "personal_info": {
                "name": "",
                "email": "",
                "phone": "",
                "address": "",
                "linkedin": "",
                "github": "",
                "website": ""
            },
            "sections": {
                "objective": {
                    "content": "",
                    "ai_generated": False,
                    "last_ai_update": None
                },
                "experience": {
                    "content": "",
                    "ai_generated": False,
                    "last_ai_update": None
                },
                "skills": {
                    "content": "",
                    "ai_generated": False,
                    "last_ai_update": None
                },
                "education": {
                    "content": "",
                    "ai_generated": False,
                    "last_ai_update": None
                },
                "projects": {
                    "content": "",
                    "ai_generated": False,
                    "last_ai_update": None
                },
                "achievements": {
                    "content": "",
                    "ai_generated": False,
                    "last_ai_update": None
                },
                "languages": {
                    "content": "",
                    "ai_generated": False,
                    "last_ai_update": None
                },
                "activities": {
                    "content": "",
                    "ai_generated": False,
                    "last_ai_update": None
                },
                "references": {
                    "content": "",
                    "ai_generated": False,
                    "last_ai_update": None
                },
                "about_me": {
                    "content": "",
                    "ai_generated": False,
                    "last_ai_update": None
                }
            },
            "ats_score": {
                "score": 0,
                "last_calculated": None,
                "suggestions": []
            },
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "is_active": True
        }
        result = mongo.db.resumes.insert_one(resume_data)
        return result.inserted_id
    
    @staticmethod
    def find_by_user(mongo, user_id):
        """Find all resumes for a user"""
        return list(mongo.db.resumes.find(
            {"user_id": ObjectId(user_id), "is_active": True}
        ).sort("updated_at", -1))
    
    @staticmethod
    def find_by_id(mongo, resume_id, user_id=None):
        """Find resume by ID"""
        query = {"_id": ObjectId(resume_id), "is_active": True}
        if user_id:
            query["user_id"] = ObjectId(user_id)
        return mongo.db.resumes.find_one(query)
    
    @staticmethod
    def update_resume(mongo, resume_id, user_id, update_data):
        """Update resume data"""
        update_data["updated_at"] = datetime.utcnow()
        result = mongo.db.resumes.update_one(
            {"_id": ObjectId(resume_id), "user_id": ObjectId(user_id)},
            {"$set": update_data}
        )
        return result.modified_count > 0
    
    @staticmethod
    def delete_resume(mongo, resume_id, user_id):
        """Soft delete a resume"""
        result = mongo.db.resumes.update_one(
            {"_id": ObjectId(resume_id), "user_id": ObjectId(user_id)},
            {"$set": {"is_active": False, "updated_at": datetime.utcnow()}}
        )
        return result.modified_count > 0

class ThemeModel:
    """Theme model for resume themes"""
    
    @staticmethod
    def get_all_themes(mongo):
        """Get all available themes"""
        return list(mongo.db.themes.find({"is_active": True}))
    
    @staticmethod
    def get_theme_by_name(mongo, theme_name):
        """Get theme by name"""
        return mongo.db.themes.find_one({"name": theme_name, "is_active": True})

class AIGenerationModel:
    """Model for tracking AI generations"""
    
    @staticmethod
    def log_generation(mongo, user_id, resume_id, section, prompt, generated_content, model_used="gpt-3.5-turbo", tokens_used=0):
        """Log AI generation for tracking"""
        generation_data = {
            "user_id": ObjectId(user_id),
            "resume_id": ObjectId(resume_id),
            "section": section,
            "prompt": prompt,
            "generated_content": generated_content,
            "model_used": model_used,
            "tokens_used": tokens_used,
            "created_at": datetime.utcnow()
        }
        result = mongo.db.ai_generations.insert_one(generation_data)
        return result.inserted_id

# Utility functions
def allowed_file(filename, allowed_extensions={'png', 'jpg', 'jpeg', 'gif'}):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def generate_unique_filename(filename):
    """Generate unique filename for uploads"""
    if filename:
        file_extension = filename.rsplit('.', 1)[1].lower() if '.' in filename else 'jpg'
        return f"{uuid.uuid4()}.{file_extension}"
    return None

# Default themes data
DEFAULT_THEMES = [
    {
        "name": "professional",
        "display_name": "Professional",
        "description": "Clean and professional design perfect for corporate roles",
        "preview_image": "static/theme_previews/professional.jpg",
        "css_template": "themes/professional.css",
        "default_colors": {
            "primary": "#007bff",
            "secondary": "#6c757d",
            "background": "#ffffff",
            "text": "#333333"
        },
        "is_active": True,
        "created_at": datetime.utcnow()
    },
    {
        "name": "modern",
        "display_name": "Modern",
        "description": "Contemporary design with clean lines and modern typography",
        "preview_image": "static/theme_previews/modern.jpg",
        "css_template": "themes/modern.css",
        "default_colors": {
            "primary": "#8b45ff",
            "secondary": "#6c757d",
            "background": "#ffffff",
            "text": "#333333"
        },
        "is_active": True,
        "created_at": datetime.utcnow()
    },
    {
        "name": "creative",
        "display_name": "Creative",
        "description": "Bold and creative design for artistic and design roles",
        "preview_image": "static/theme_previews/creative.jpg",
        "css_template": "themes/creative.css",
        "default_colors": {
            "primary": "#ff6b6b",
            "secondary": "#4ecdc4",
            "background": "#ffffff",
            "text": "#333333"
        },
        "is_active": True,
        "created_at": datetime.utcnow()
    }
]
