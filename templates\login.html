<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login - SmartHire Resume Builder</title>
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='bootstrap.min.css') }}"
    />
    <style>
      body {
        background: rgba(0, 0, 0, 0.5);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
      }
      .auth-modal {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        padding: 2.5rem;
        width: 100%;
        max-width: 420px;
        position: relative;
        animation: slideIn 0.3s ease-out;
      }
      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      .close-btn {
        position: absolute;
        top: 20px;
        right: 25px;
        background: none;
        border: none;
        font-size: 24px;
        color: #999;
        cursor: pointer;
        text-decoration: none;
      }
      .close-btn:hover {
        color: #333;
      }
      .auth-header {
        text-align: center;
        margin-bottom: 2rem;
      }
      .auth-header h2 {
        color: #333;
        font-weight: 700;
        font-size: 1.8rem;
        margin-bottom: 0.5rem;
      }
      .auth-header p {
        color: #666;
        margin: 0;
        font-size: 0.95rem;
      }
      .form-group {
        margin-bottom: 1.5rem;
      }
      .form-label {
        color: #333;
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        display: block;
      }
      .form-control {
        border-radius: 12px;
        border: 1px solid #e0e0e0;
        padding: 15px 18px;
        font-size: 1rem;
        background-color: #f8f9fa;
        width: 100%;
        box-sizing: border-box;
      }
      .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        background-color: white;
        outline: none;
      }
      .password-field {
        position: relative;
      }
      .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        font-size: 18px;
      }
      .btn-auth {
        background-color: #000;
        border: none;
        border-radius: 12px;
        padding: 15px;
        font-weight: 600;
        font-size: 1rem;
        width: 100%;
        color: white;
        cursor: pointer;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      .btn-auth:hover {
        background-color: #333;
      }
      .auth-switch {
        text-align: center;
        margin-top: 1.5rem;
        font-size: 0.9rem;
      }
      .auth-switch a {
        color: #007bff;
        text-decoration: none;
        font-weight: 600;
        cursor: pointer;
      }
      .auth-switch a:hover {
        text-decoration: underline;
      }
      .signup-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        position: relative;
        font-size: 2rem;
      }
      .signup-icon::after {
        content: "+";
        position: absolute;
        bottom: 5px;
        right: 5px;
        background: #007bff;
        color: white;
        width: 25px;
        height: 25px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;
      }
      .hidden {
        display: none;
      }

      /* Photo upload styles */
      .photo-upload {
        text-align: center;
        margin-bottom: 1.5rem;
      }

      .photo-preview {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #e8f2ff 0%, #d1e7ff 100%);
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(139, 69, 255, 0.15);
      }

      .photo-preview:hover {
        background: linear-gradient(135deg, #dce9ff 0%, #c4ddff 100%);
        box-shadow: 0 6px 16px rgba(139, 69, 255, 0.25);
        transform: translateY(-2px);
      }

      .photo-preview img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
      }

      .photo-placeholder {
        color: #8b45ff;
        font-size: 2rem;
        position: relative;
      }

      .photo-placeholder::after {
        content: "⬇";
        position: absolute;
        bottom: -8px;
        right: -8px;
        background: #8b45ff;
        color: white;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        border: 2px solid white;
      }

      .photo-upload-text {
        font-size: 0.85rem;
        color: #666;
        margin-top: 0.5rem;
      }

      .file-input {
        display: none;
      }
    </style>
  </head>
  <body>
    <!-- Login Modal -->
    <div class="auth-modal" id="loginModal">
      <a href="/" class="close-btn">×</a>

      <div class="auth-header">
        <h2>Welcome Back!</h2>
        <p>Please enter your details to log in</p>
      </div>

      <form method="POST" action="/login">
        <div class="form-group">
          <label class="form-label">Email Address</label>
          <input
            type="email"
            class="form-control"
            name="username"
            placeholder="<EMAIL>"
            required
          />
        </div>

        <div class="form-group">
          <label class="form-label">Password</label>
          <div class="password-field">
            <input
              type="password"
              class="form-control"
              name="password"
              placeholder="Min 8 Characters"
              required
            />
            <button type="button" class="password-toggle">👁️</button>
          </div>
        </div>

        <button type="submit" class="btn-auth">LOGIN</button>
      </form>

      <div class="auth-switch">
        Don't have an account? <a onclick="showSignup()">Sign Up</a>
      </div>
    </div>

    <!-- Signup Modal -->
    <div class="auth-modal hidden" id="signupModal">
      <a href="/" class="close-btn">×</a>

      <div class="auth-header">
        <h2>Create an Account</h2>
        <p>Join us today by entering your details below</p>
      </div>

      <form method="POST" action="/signup" enctype="multipart/form-data">
        <!-- Photo Upload Section -->
        <div class="photo-upload">
          <div
            class="photo-preview"
            onclick="document.getElementById('photoInput').click()"
          >
            <div class="photo-placeholder" id="photoPlaceholder">📷</div>
            <img id="photoPreview" style="display: none" />
          </div>
          <input
            type="file"
            id="photoInput"
            name="photo"
            class="file-input"
            accept="image/*"
            onchange="previewPhoto(this)"
          />
          <div class="photo-upload-text">Click to upload photo</div>
        </div>

        <div class="form-group">
          <label class="form-label">Full Name</label>
          <input
            type="text"
            class="form-control"
            name="fullname"
            placeholder="John"
            required
          />
        </div>

        <div class="form-group">
          <label class="form-label">Email Address</label>
          <input
            type="email"
            class="form-control"
            name="email"
            placeholder="<EMAIL>"
            required
          />
        </div>

        <div class="form-group">
          <label class="form-label">Password</label>
          <div class="password-field">
            <input
              type="password"
              class="form-control"
              name="password"
              placeholder="Min 8 Characters"
              required
            />
            <button type="button" class="password-toggle">👁️</button>
          </div>
        </div>

        <button type="submit" class="btn-auth">SIGN UP</button>
      </form>

      <div class="auth-switch">
        Already an account? <a onclick="showLogin()">Login</a>
      </div>
    </div>

    <script>
      function showSignup() {
        document.getElementById("loginModal").classList.add("hidden");
        document.getElementById("signupModal").classList.remove("hidden");
      }

      function showLogin() {
        document.getElementById("signupModal").classList.add("hidden");
        document.getElementById("loginModal").classList.remove("hidden");
      }

      // Photo preview functionality
      function previewPhoto(input) {
        if (input.files && input.files[0]) {
          const reader = new FileReader();
          reader.onload = function (e) {
            const preview = document.getElementById("photoPreview");
            const placeholder = document.getElementById("photoPlaceholder");

            preview.src = e.target.result;
            preview.style.display = "block";
            placeholder.style.display = "none";
          };
          reader.readAsDataURL(input.files[0]);
        }
      }

      // Password toggle functionality
      document.querySelectorAll(".password-toggle").forEach((btn) => {
        btn.addEventListener("click", function () {
          const input = this.previousElementSibling;
          if (input.type === "password") {
            input.type = "text";
            this.textContent = "🙈";
          } else {
            input.type = "password";
            this.textContent = "👁️";
          }
        });
      });
    </script>
  </body>
</html>
