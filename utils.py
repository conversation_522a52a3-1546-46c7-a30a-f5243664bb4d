# SmartHire - Utility Functions for AI and ATS

import openai
import re
from datetime import datetime
import json

class AIContentGenerator:
    """AI content generation utilities"""
    
    @staticmethod
    def generate_objective(user_data, job_title="", industry=""):
        """Generate professional objective using AI"""
        try:
            prompt = f"""
            Generate a professional resume objective for a candidate with the following information:
            Name: {user_data.get('name', '')}
            Skills: {user_data.get('skills', '')}
            Experience: {user_data.get('experience', '')}
            Job Title Target: {job_title}
            Industry: {industry}
            
            Write a compelling 2-3 sentence objective that highlights their strengths and career goals.
            Make it professional, concise, and ATS-friendly.
            """
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a professional resume writer. Create compelling, ATS-optimized resume content."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=150,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
        except Exception as e:
            print(f"AI Generation Error: {e}")
            return "Dedicated professional seeking to leverage skills and experience to contribute to organizational success."
    
    @staticmethod
    def generate_experience_description(job_title, company, responsibilities=""):
        """Generate experience description using AI"""
        try:
            prompt = f"""
            Generate a professional experience description for:
            Job Title: {job_title}
            Company: {company}
            Current responsibilities/achievements: {responsibilities}
            
            Create 3-4 bullet points that highlight key achievements and responsibilities.
            Use action verbs and include quantifiable results where possible.
            Make it ATS-friendly with relevant keywords.
            """
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a professional resume writer. Create compelling, ATS-optimized resume content with quantifiable achievements."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
        except Exception as e:
            print(f"AI Generation Error: {e}")
            return f"• Performed key responsibilities in {job_title} role\n• Collaborated with team members to achieve objectives\n• Contributed to company goals and initiatives"
    
    @staticmethod
    def generate_achievements(user_data):
        """Generate achievements section using AI"""
        try:
            prompt = f"""
            Based on the following information, generate professional achievements:
            Skills: {user_data.get('skills', '')}
            Experience: {user_data.get('experience', '')}
            Projects: {user_data.get('projects', '')}
            
            Create 3-4 achievement statements that showcase accomplishments.
            Include specific metrics and results where possible.
            Make them impactful and ATS-friendly.
            """
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a professional resume writer. Create compelling achievement statements with quantifiable results."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
        except Exception as e:
            print(f"AI Generation Error: {e}")
            return "• Demonstrated strong performance in professional responsibilities\n• Achieved key objectives and contributed to team success\n• Recognized for dedication and quality work"
    
    @staticmethod
    def generate_about_me(user_data):
        """Generate about me section using AI"""
        try:
            prompt = f"""
            Create a professional "About Me" section for a resume based on:
            Name: {user_data.get('name', '')}
            Skills: {user_data.get('skills', '')}
            Experience: {user_data.get('experience', '')}
            Activities: {user_data.get('activities', '')}
            
            Write a compelling 2-3 sentence summary that showcases personality and professional strengths.
            Make it engaging and professional.
            """
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a professional resume writer. Create engaging personal summaries that showcase both professional skills and personality."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=150,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
        except Exception as e:
            print(f"AI Generation Error: {e}")
            return "Passionate professional with a strong commitment to excellence and continuous learning."

class ATSScoreAnalyzer:
    """ATS Score analysis and optimization"""
    
    # Common ATS-friendly keywords by category
    ATS_KEYWORDS = {
        'technical': ['python', 'javascript', 'java', 'react', 'node.js', 'sql', 'html', 'css', 'git', 'aws', 'docker', 'kubernetes'],
        'management': ['leadership', 'management', 'team lead', 'project management', 'strategic planning', 'budget management'],
        'soft_skills': ['communication', 'collaboration', 'problem-solving', 'analytical', 'creative', 'adaptable', 'detail-oriented'],
        'action_verbs': ['achieved', 'developed', 'implemented', 'managed', 'created', 'improved', 'optimized', 'delivered', 'led', 'designed']
    }
    
    @staticmethod
    def calculate_ats_score(resume_data):
        """Calculate ATS compatibility score"""
        score = 0
        suggestions = []
        
        # Combine all text content
        all_text = ""
        sections = resume_data.get('sections', {})
        personal_info = resume_data.get('personal_info', {})
        
        # Add personal info
        all_text += f" {personal_info.get('name', '')} {personal_info.get('email', '')}"
        
        # Add all sections
        for section_name, section_data in sections.items():
            if isinstance(section_data, dict):
                all_text += f" {section_data.get('content', '')}"
            else:
                all_text += f" {section_data}"
        
        all_text = all_text.lower()
        
        # Check for keywords (40 points max)
        keyword_score = 0
        found_keywords = []
        
        for category, keywords in ATSScoreAnalyzer.ATS_KEYWORDS.items():
            category_found = 0
            for keyword in keywords:
                if keyword.lower() in all_text:
                    category_found += 1
                    found_keywords.append(keyword)
            
            # Score based on keyword diversity
            if category_found > 0:
                keyword_score += min(10, category_found * 2)
        
        score += min(40, keyword_score)
        
        # Check for contact information (20 points max)
        contact_score = 0
        if personal_info.get('email') and '@' in personal_info.get('email', ''):
            contact_score += 5
        if personal_info.get('phone'):
            contact_score += 5
        if personal_info.get('linkedin'):
            contact_score += 5
        if personal_info.get('name'):
            contact_score += 5
        
        score += contact_score
        
        # Check for section completeness (25 points max)
        section_score = 0
        required_sections = ['objective', 'experience', 'skills']
        for section in required_sections:
            if sections.get(section, {}).get('content', '').strip():
                section_score += 8
        
        score += min(25, section_score)
        
        # Check for quantifiable achievements (15 points max)
        numbers_pattern = r'\d+[%$]?|\d+\+|increased|decreased|improved|reduced'
        if re.search(numbers_pattern, all_text):
            score += 15
        else:
            suggestions.append("Add quantifiable achievements with numbers and percentages")
        
        # Generate suggestions based on score
        if score < 60:
            suggestions.extend([
                "Add more relevant keywords for your industry",
                "Include specific technical skills",
                "Add quantifiable achievements and results"
            ])
        
        if contact_score < 15:
            suggestions.append("Complete your contact information including LinkedIn profile")
        
        if keyword_score < 20:
            suggestions.append("Include more industry-relevant keywords throughout your resume")
        
        if not re.search(r'(bachelor|master|degree|certification|diploma)', all_text):
            suggestions.append("Consider adding education or certification information")
        
        return {
            'score': min(100, score),
            'last_calculated': datetime.utcnow(),
            'suggestions': suggestions[:5],  # Limit to top 5 suggestions
            'found_keywords': found_keywords[:10]  # Show top 10 found keywords
        }
    
    @staticmethod
    def get_keyword_suggestions(job_title="", industry=""):
        """Get keyword suggestions based on job title and industry"""
        suggestions = []
        
        # Add general suggestions
        suggestions.extend(ATSScoreAnalyzer.ATS_KEYWORDS['action_verbs'][:5])
        suggestions.extend(ATSScoreAnalyzer.ATS_KEYWORDS['soft_skills'][:3])
        
        # Add specific suggestions based on job title
        if 'developer' in job_title.lower() or 'engineer' in job_title.lower():
            suggestions.extend(ATSScoreAnalyzer.ATS_KEYWORDS['technical'][:8])
        
        if 'manager' in job_title.lower() or 'lead' in job_title.lower():
            suggestions.extend(ATSScoreAnalyzer.ATS_KEYWORDS['management'][:5])
        
        return list(set(suggestions))  # Remove duplicates

def format_resume_for_pdf(resume_data, theme="professional"):
    """Format resume data for PDF generation"""
    formatted_data = {
        'personal_info': resume_data.get('personal_info', {}),
        'sections': resume_data.get('sections', {}),
        'theme': theme,
        'theme_settings': resume_data.get('theme_settings', {}),
        'generated_at': datetime.utcnow().strftime('%Y-%m-%d')
    }
    
    return formatted_data
