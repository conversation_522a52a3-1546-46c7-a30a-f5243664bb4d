<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartHire - AI Resume Builder</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap.min.css') }}">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .auth-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
            min-height: 500px;
        }
        .auth-left {
            background: linear-gradient(135deg, #8b45ff 0%, #7c3aed 100%);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        .auth-right {
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .brand-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        .brand-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        .form-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 2rem;
            text-align: center;
        }
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        .form-control:focus {
            border-color: #8b45ff;
            box-shadow: 0 0 0 0.2rem rgba(139, 69, 255, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #8b45ff 0%, #7c3aed 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            width: 100%;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 69, 255, 0.3);
        }
        .auth-link {
            text-align: center;
            margin-top: 1.5rem;
            color: #666;
        }
        .auth-link a {
            color: #8b45ff;
            text-decoration: none;
            font-weight: 600;
            cursor: pointer;
        }
        .auth-link a:hover {
            text-decoration: underline;
        }
        .password-toggle {
            position: relative;
        }
        .password-toggle .toggle-btn {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            font-size: 1.1rem;
        }
        .alert {
            border-radius: 10px;
            margin-bottom: 1rem;
        }
        .loading {
            display: none;
        }
        .photo-upload {
            text-align: center;
            margin-bottom: 1rem;
        }
        .photo-preview {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px dashed #8b45ff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            cursor: pointer;
            background: #f8f9fa;
            overflow: hidden;
        }
        .photo-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .photo-preview .placeholder {
            color: #8b45ff;
            font-size: 2rem;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="row g-0 h-100">
            <!-- Left Side - Branding -->
            <div class="col-md-6 auth-left">
                <div>
                    <h1 class="brand-title">SmartHire</h1>
                    <p class="brand-subtitle">
                        Create professional, ATS-optimized resumes with the power of AI. 
                        Stand out from the crowd and land your dream job.
                    </p>
                </div>
            </div>
            
            <!-- Right Side - Auth Forms -->
            <div class="col-md-6 auth-right">
                <!-- Login Form -->
                <div id="loginForm">
                    <h2 class="form-title">Welcome Back</h2>
                    
                    <!-- Alert Messages -->
                    <div id="alert-container"></div>
                    
                    <form id="loginFormElement">
                        <div class="mb-3">
                            <input type="email" name="email" id="loginEmail" class="form-control" placeholder="Email Address" required>
                        </div>
                        
                        <div class="mb-3 password-toggle">
                            <input type="password" name="password" id="loginPassword" class="form-control" placeholder="Password" required>
                            <button type="button" class="toggle-btn" onclick="togglePassword('loginPassword', 'loginToggleIcon')">
                                <span id="loginToggleIcon">👁️</span>
                            </button>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" id="loginBtn">
                            <span class="loading spinner-border spinner-border-sm me-2" role="status"></span>
                            Sign In
                        </button>
                    </form>
                    
                    <div class="auth-link">
                        Don't have an account? <a onclick="showSignup()">Sign up</a>
                    </div>
                </div>

                <!-- Signup Form -->
                <div id="signupForm" style="display: none;">
                    <h2 class="form-title">Create Account</h2>
                    
                    <!-- Alert Messages -->
                    <div id="signup-alert-container"></div>
                    
                    <form id="signupFormElement" enctype="multipart/form-data">
                        <div class="photo-upload">
                            <div class="photo-preview" onclick="document.getElementById('photoInput').click()">
                                <div class="placeholder" id="photoPlaceholder">📷</div>
                                <img id="photoPreview" style="display: none;">
                            </div>
                            <input type="file" id="photoInput" name="photo" accept="image/*" style="display: none;" onchange="previewPhoto(this)">
                            <small class="text-muted">Click to upload photo (optional)</small>
                        </div>
                        
                        <div class="mb-3">
                            <input type="text" name="name" id="signupName" class="form-control" placeholder="Full Name" required>
                        </div>
                        
                        <div class="mb-3">
                            <input type="email" name="email" id="signupEmail" class="form-control" placeholder="Email Address" required>
                        </div>
                        
                        <div class="mb-3 password-toggle">
                            <input type="password" name="password" id="signupPassword" class="form-control" placeholder="Password (min 6 characters)" required>
                            <button type="button" class="toggle-btn" onclick="togglePassword('signupPassword', 'signupToggleIcon')">
                                <span id="signupToggleIcon">👁️</span>
                            </button>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" id="signupBtn">
                            <span class="loading spinner-border spinner-border-sm me-2" role="status"></span>
                            Create Account
                        </button>
                    </form>
                    
                    <div class="auth-link">
                        Already have an account? <a onclick="showLogin()">Sign in</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='bootstrap.min.js') }}"></script>
    <script>
        // Toggle between login and signup forms
        function showSignup() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('signupForm').style.display = 'block';
            clearAlerts();
        }
        
        function showLogin() {
            document.getElementById('signupForm').style.display = 'none';
            document.getElementById('loginForm').style.display = 'block';
            clearAlerts();
        }
        
        // Password visibility toggle
        function togglePassword(fieldId, iconId) {
            const passwordField = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(iconId);
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.textContent = '🙈';
            } else {
                passwordField.type = 'password';
                toggleIcon.textContent = '👁️';
            }
        }
        
        // Photo preview
        function previewPhoto(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('photoPlaceholder').style.display = 'none';
                    const preview = document.getElementById('photoPreview');
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }
        
        // Alert functions
        function showAlert(containerId, message, type = 'danger') {
            const container = document.getElementById(containerId);
            container.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        function clearAlerts() {
            document.getElementById('alert-container').innerHTML = '';
            document.getElementById('signup-alert-container').innerHTML = '';
        }
        
        // Loading state
        function setLoading(btnId, loading) {
            const btn = document.getElementById(btnId);
            const spinner = btn.querySelector('.loading');
            
            if (loading) {
                btn.disabled = true;
                spinner.style.display = 'inline-block';
            } else {
                btn.disabled = false;
                spinner.style.display = 'none';
            }
        }
        
        // Login form submission
        document.getElementById('loginFormElement').addEventListener('submit', async function(e) {
            e.preventDefault();
            setLoading('loginBtn', true);
            clearAlerts();
            
            const formData = new FormData(this);
            const data = {
                email: formData.get('email'),
                password: formData.get('password')
            };
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    localStorage.setItem('access_token', result.access_token);
                    localStorage.setItem('user', JSON.stringify(result.user));
                    window.location.href = '/dashboard';
                } else {
                    showAlert('alert-container', result.error || 'Login failed');
                }
            } catch (error) {
                showAlert('alert-container', 'Network error. Please try again.');
            } finally {
                setLoading('loginBtn', false);
            }
        });
        
        // Signup form submission
        document.getElementById('signupFormElement').addEventListener('submit', async function(e) {
            e.preventDefault();
            setLoading('signupBtn', true);
            clearAlerts();
            
            const formData = new FormData(this);
            
            try {
                const response = await fetch('/api/auth/signup', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    localStorage.setItem('access_token', result.access_token);
                    localStorage.setItem('user', JSON.stringify(result.user));
                    window.location.href = '/dashboard';
                } else {
                    showAlert('signup-alert-container', result.error || 'Signup failed');
                }
            } catch (error) {
                showAlert('signup-alert-container', 'Network error. Please try again.');
            } finally {
                setLoading('signupBtn', false);
            }
        });
    </script>
</body>
</html>
