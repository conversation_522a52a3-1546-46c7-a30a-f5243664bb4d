# SmartHire Environment Variables
# Copy this file to .env and fill in your actual values

# Database Configuration
MONGO_URI=mongodb://localhost:27017/smarthire_db

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production

# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True

# Upload Configuration
MAX_CONTENT_LENGTH=16777216  # 16MB in bytes

# Email Configuration (for future features)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Security
SECRET_KEY=your-flask-secret-key-change-this-in-production
