<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Resume Builder - Complete Your Profile</title>
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='bootstrap.min.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='home.css') }}"
    />
    <style>
      body {
        background: linear-gradient(135deg, #e8f2ff 0%, #d1e7ff 100%);
        min-height: 100vh;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      }
      .form-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(139, 69, 255, 0.1);
        padding: 2rem;
        margin: 2rem 0;
      }
      .form-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 12px;
        border-left: 4px solid #8b45ff;
      }
      .section-title {
        color: #8b45ff;
        font-weight: 600;
        font-size: 1.2rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
      }
      .section-icon {
        margin-right: 0.5rem;
        font-size: 1.3rem;
      }
      .form-control {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 0.75rem;
        transition: all 0.3s ease;
      }
      .form-control:focus {
        border-color: #8b45ff;
        box-shadow: 0 0 0 0.2rem rgba(139, 69, 255, 0.25);
      }
      .btn-primary {
        background: linear-gradient(135deg, #8b45ff 0%, #7c3aed 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
      }
      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(139, 69, 255, 0.3);
      }
      .progress-bar {
        background: linear-gradient(135deg, #8b45ff 0%, #7c3aed 100%);
        height: 6px;
        border-radius: 3px;
      }
      .navbar-brand {
        color: #8b45ff !important;
        font-weight: 700;
        font-size: 1.5rem;
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-light bg-white py-3">
      <div class="container">
        <a class="navbar-brand" href="/">Resume Builder</a>
      </div>
    </nav>

    <div class="container">
      <div class="row justify-content-center">
        <div class="col-lg-8">
          <div class="form-container">
            <!-- Header -->
            <div class="text-center mb-4">
              <h1 class="mb-2" style="color: #8b45ff; font-weight: 700">
                Complete Your Resume
              </h1>
              <p class="text-muted">
                Fill in the details to create your professional resume
              </p>

              <!-- Progress Bar -->
              <div class="progress mb-4" style="height: 6px">
                <div
                  class="progress-bar"
                  role="progressbar"
                  style="width: 75%"
                ></div>
              </div>
            </div>

            <form action="/submit" method="POST">
              <!-- Personal Information Section -->
              <div class="form-section">
                <h3 class="section-title">
                  <span class="section-icon">👤</span>
                  Personal Information
                </h3>
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="languages" class="form-label">Languages</label>
                    <input
                      type="text"
                      id="languages"
                      name="languages"
                      class="form-control"
                      placeholder="e.g., English, Spanish, French"
                      required
                    />
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="activities" class="form-label"
                      >Activities & Interests</label
                    >
                    <input
                      type="text"
                      id="activities"
                      name="activities"
                      class="form-control"
                      placeholder="e.g., Photography, Reading, Sports"
                      required
                    />
                  </div>
                </div>
                <div class="mb-3">
                  <label for="about_me" class="form-label">About Me</label>
                  <textarea
                    id="about_me"
                    name="about_me"
                    rows="3"
                    class="form-control"
                    placeholder="Write a brief description about yourself..."
                  ></textarea>
                </div>
              </div>

              <!-- Professional Skills Section -->
              <div class="form-section">
                <h3 class="section-title">
                  <span class="section-icon">🛠️</span>
                  Professional Skills
                </h3>
                <div class="mb-3">
                  <label for="skills" class="form-label"
                    >Technical Skills</label
                  >
                  <textarea
                    id="skills"
                    name="skills"
                    rows="3"
                    class="form-control"
                    placeholder="e.g., JavaScript, Python, Project Management, Adobe Creative Suite..."
                  ></textarea>
                </div>
                <div class="mb-3">
                  <label for="objective" class="form-label"
                    >Career Objective</label
                  >
                  <textarea
                    id="objective"
                    name="objective"
                    rows="3"
                    class="form-control"
                    placeholder="Describe your career goals and what you're looking for..."
                  ></textarea>
                </div>
              </div>

              <!-- Experience & Projects Section -->
              <div class="form-section">
                <h3 class="section-title">
                  <span class="section-icon">💼</span>
                  Experience & Projects
                </h3>
                <div class="mb-3">
                  <label for="experience" class="form-label"
                    >Work Experience</label
                  >
                  <textarea
                    id="experience"
                    name="experience"
                    rows="4"
                    class="form-control"
                    placeholder="Describe your work experience, including job titles, companies, and key responsibilities..."
                  ></textarea>
                </div>
                <div class="mb-3">
                  <label for="projects" class="form-label">Projects</label>
                  <textarea
                    id="projects"
                    name="projects"
                    rows="3"
                    class="form-control"
                    placeholder="Describe your key projects, including technologies used and outcomes..."
                  ></textarea>
                </div>
              </div>

              <!-- Achievements & References Section -->
              <div class="form-section">
                <h3 class="section-title">
                  <span class="section-icon">🏆</span>
                  Achievements & References
                </h3>
                <div class="mb-3">
                  <label for="achievements" class="form-label"
                    >Achievements & Awards</label
                  >
                  <textarea
                    id="achievements"
                    name="achievements"
                    rows="3"
                    class="form-control"
                    placeholder="List your achievements, awards, certifications, or notable accomplishments..."
                  ></textarea>
                </div>
                <div class="mb-3">
                  <label for="reference" class="form-label">References</label>
                  <textarea
                    id="reference"
                    name="reference"
                    rows="3"
                    class="form-control"
                    placeholder="Provide reference contacts or mention 'Available upon request'..."
                  ></textarea>
                </div>
              </div>

              <!-- Submit Button -->
              <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg px-5">
                  🚀 Generate My Resume
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
